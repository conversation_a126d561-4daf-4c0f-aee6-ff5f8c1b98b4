import { motion } from "framer-motion";
import PrivacyContent from "../components/sections/PrivacyContent";
import PrivacyContentEng from "@/components/sections/PrivacyContentEng";

export default function PrivacyPolicy() {
  return (
    <section className="py-32 bg-gray-950 text-gray-200">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 via-gray-900/50 to-gray-950" />

      {/* Animated Gradient Orbs */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 blur-3xl animate-drift" />
        <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-l from-cyan-500/30 to-blue-500/30 blur-3xl animate-drift-slow" />
      </div>

      {/* <PERSON><PERSON> */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:64px_64px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,black_40%,transparent_100%)]" />
      {/* <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" /> */}
      <div className="container max-w-3xl mx-auto ">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          {/* OLD CONTENT - COMMENTED OUT
          <div>
            <h1 className="text-3xl font-bold mb-8">Datenschutzerklärung</h1>
            <div className="space-y-8">
              <section>
                <h2 className="text-xl font-semibold mb-3">Präambel</h2>
                <p className="text-muted-foreground">
                  Mit der folgenden Datenschutzerklärung möchten wir Sie darüber
                  aufklären, welche Arten Ihrer personenbezogenen Daten
                  (nachfolgend auch kurz als "Daten" bezeichnet) wir zu welchen
                  Zwecken und in welchem Umfang im Rahmen der Bereitstellung
                  unserer Applikation verarbeiten.
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  Stand: 28. Dezember 2023
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Verantwortlicher</h2>
                <p>Gainback GmbH</p>
                <p>Falkenstein 16a</p>
                <p>97499 Donnersdorf</p>
                <p className="mt-2">E-Mail: <EMAIL></p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Verarbeitete Daten
                </h2>
                <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                  <li>Nutzungsdaten</li>
                  <li>Anwendung-Updates</li>
                  <li>Social-Media-Name</li>
                  <li>E-Mail</li>
                  <li>Kameraberechtigung</li>
                  <li>Standortdaten</li>
                  <li>Fotobibliothekberechtigung</li>
                  <li>Zahlungsdaten</li>
                </ul>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Zwecke der Verarbeitung
                </h2>
                <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                  <li>Erbringung vertraglicher Leistungen</li>
                  <li>Kontaktanfragen und Kommunikation</li>
                  <li>Sicherheitsmaßnahmen</li>
                  <li>Direktmarketing</li>
                  <li>Verwaltung und Beantwortung von Anfragen</li>
                  <li>Marketing</li>
                  <li>Bereitstellung unseres Onlineangebotes</li>
                </ul>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Dienste und Technologien
                </h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-medium mb-2">
                      Google Analytics für Firebase
                    </h3>
                    <p className="text-muted-foreground">
                      Wir nutzen Google Analytics für Firebase für
                      Analysezwecke. Die Datenverarbeitung findet in den USA
                      statt. Weitere Informationen finden Sie unter:{" "}
                      <a
                        href="https://policies.google.com/privacy"
                        className="text-orange-500 hover:underline hover:cursor-pointer"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Google Privacy Policy
                      </a>
                    </p>
                  </div>
                  <div>
                    <h3 className="font-medium mb-2">Stripe</h3>
                    <p className="text-muted-foreground">
                      Für die Zahlungsabwicklung nutzen wir Stripe. Weitere
                      Informationen finden Sie unter:{" "}
                      <a
                        href="https://stripe.com/de/privacy"
                        className="text-orange-500 hover:underline hover:cursor-pointer"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        Stripe Privacy Policy
                      </a>
                    </p>
                  </div>
                </div>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Ihre Rechte</h2>
                <p className="text-muted-foreground">
                  Sie haben das Recht auf Auskunft, Berichtigung, Löschung und
                  Einschränkung der Verarbeitung der personenbezogenen Daten.
                  Zudem steht Ihnen ein Widerspruchsrecht gegen die Verarbeitung
                  sowie ein Recht auf Datenübertragbarkeit zu.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">Kontakt</h2>
                <p className="text-muted-foreground">
                  Bei Fragen zur Verarbeitung Ihrer personenbezogenen Daten
                  sowie zu Ihren Rechten können Sie sich jederzeit an uns
                  wenden.
                </p>
              </section>
            </div>
          </div>
          END OF OLD CONTENT */}

          {/* NEW CONTENT */}

          {/* Additional Privacy Policy Content */}
          <PrivacyContent />
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="mt-5 bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <PrivacyContentEng />
        </motion.div>
      </div>
    </section>
  );
}
