import { useEffect, useRef, useState } from "react";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";
import { useParallax } from "../../hooks/use-parallax";
import TypewriterEffect from "../ui/TypewriterEffect";
import VideoModal from "../ui/VideoModal";
import { navigateToSection } from "../../utils/navigation";
import card from "/carrdlogo.svg";
import gainback from "/gainback.png";
// import card from "../../../public/gainback.png";

const TRUSTED_COMPANIES = [
  // {
  //   name: "Audi",
  //   logo: "https://upload.wikimedia.org/wikipedia/commons/9/92/Audi-Logo_2016.svg",
  // },
  // {
  //   name: "Mercedes-Benz",
  //   logo: "https://upload.wikimedia.org/wikipedia/commons/9/90/Mercedes-Logo.svg",
  // },
  // {
  //   name: "Volkswagen",
  //   logo: "https://upload.wikimedia.org/wikipedia/commons/6/6d/Volkswagen_logo_2019.svg",
  // },
  {
    name: "CAARD",
    logo: "https://cdn.caard.net/carrdlogo.svg",
    className:
      "h-10 w-auto brightness-0 invert opacity-70 hover:opacity-100 transition-opacity",
  },
  {
    name: "Gainback",
    logo: "https://cdn.caard.net/gainback.png",
    className:
      "h-14 w-auto brightness-0 invert opacity-70 hover:opacity-100 transition-opacity",
  },
];

export default function Hero() {
  const containerRef = useRef<HTMLDivElement>(null);
  const parallaxRef1 = useParallax(40);
  const parallaxRef2 = useParallax(20);
  const [isModalOpen, setModalOpen] = useState(false);

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"],
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);
  const scale = useSpring(useTransform(scrollYProgress, [0, 0.5], [1, 0.8]));

  useEffect(() => {
    const updateMousePosition = (e: MouseEvent) => {
      if (!containerRef.current) return;
      const { clientX, clientY } = e;
      const { width, height, left, top } =
        containerRef.current.getBoundingClientRect();
      const x = clientX - left;
      const y = clientY - top;
      containerRef.current.style.setProperty("--mouse-x", `${x}px`);
      containerRef.current.style.setProperty("--mouse-y", `${y}px`);
    };

    window.addEventListener("mousemove", updateMousePosition);
    return () => window.removeEventListener("mousemove", updateMousePosition);
  }, []);

  return (
    <motion.section
      ref={containerRef}
      className="relative min-h-[100svh] flex items-center justify-center overflow-hidden pt-32 pb-20"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
    >
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 via-gray-900/50 to-gray-950" />

      {/* Animated Gradient Orbs */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 blur-3xl animate-drift" />
        <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-l from-cyan-500/30 to-blue-500/30 blur-3xl animate-drift-slow" />
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:64px_64px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,black_40%,transparent_100%)]" />

      {/* Content Container */}
      <div className="container relative mx-auto px-4 sm:px-6 z-10">
        <motion.div
          style={{ y, opacity, scale }}
          className="text-center max-w-4xl mx-auto relative"
        >
          {/* Floating Elements */}
          <motion.div
            ref={parallaxRef1}
            className="absolute -top-20 -left-20 w-40 h-40 bg-blue-500/20 rounded-full blur-xl hidden md:block"
            animate={{
              scale: [1, 1.2, 1],
              rotate: [0, 90, 0],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "linear",
            }}
          />
          <motion.div
            ref={parallaxRef2}
            className="absolute top-20 -right-20 w-60 h-60 bg-purple-500/20 rounded-full blur-xl hidden md:block"
            animate={{
              scale: [1.2, 1, 1.2],
              rotate: [90, 0, 90],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "linear",
            }}
          />

          {/* Trust Badge */}
          <motion.div
            className="mb-4"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <div className="flex flex-col items-center gap-4">
              {/* <div className="flex items-center gap-2 text-sm">
                <span className="text-gray-400">
                  Vertraut von führenden Unternehmen
                </span>
                <span className="px-2 py-1 rounded-full bg-white/5 text-white font-medium">
                  500+ Firmen
                </span>
              </div> */}
              <div className="flex justify-center items-center gap-x-6">
                {/* <motion.div
                  key={"CAARD"}
                  whileHover={{ scale: 1.05 }}
                  className="relative"
                >
                  <img
                    src={card}
                    alt={"CAARD"}
                    className={
                      "h-10 w-auto brightness-0 invert opacity-70 hover:opacity-100 transition-opacity"
                    }
                  />
                </motion.div>
                <motion.div
                  key={"Gainback"}
                  whileHover={{ scale: 1.05 }}
                  className="relative"
                >
                  <img
                    src={gainback}
                    alt={"Gainback"}
                    className={
                      "h-14 w-auto brightness-0 invert opacity-70 hover:opacity-100 transition-opacity"
                    }
                  />
                </motion.div> */}
                {/* {TRUSTED_COMPANIES.map((company) => (
                  <motion.div
                    key={company.name}
                    whileHover={{ scale: 1.05 }}
                    className="relative"
                  >
                    <img
                      src={company.logo}
                      alt={company.name}
                      className={company.className}
                    />
                  </motion.div>
                ))} */}
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <motion.h1
            className="text-4xl sm:text-5xl md:text-7xl font-bold mb-8 tracking-[-0.02em] !leading-[1.4]"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 animate-gradient">
              Optimieren Sie Ihre
            </span>
            <div className="h-[1.1em] flex items-center justify-center">
              <TypewriterEffect />
            </div>
          </motion.h1>

          <motion.p
            className="text-lg sm:text-xl md:text-2xl text-gray-300 mb-12 leading-relaxed font-light tracking-[-0.01em] px-4 sm:px-0"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            wie Sie Inhouse und mit Ihren Partnern Dokumente bearbeiten
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center px-4 sm:px-0 mb-16"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => navigateToSection("#pricing")}
              className="w-full sm:w-auto h-12 px-8 rounded-xl bg-white text-gray-900 font-medium hover:shadow-lg hover:shadow-white/10 transition-all duration-300"
            >
              Jetzt starten
            </motion.button>

            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setModalOpen(true)}
              className="w-full sm:w-auto h-12 px-8 rounded-xl text-white bg-white/5 font-medium hover:shadow-lg hover:shadow-white/5 transition-all duration-300"
            >
              Demo ansehen
            </motion.button>
          </motion.div>

          <VideoModal
            isOpen={isModalOpen}
            onClose={() => setModalOpen(false)}
            videoUrl="https://www.youtube.com/embed/si9sl-DcD3o"
          />
        </motion.div>
      </div>
    </motion.section>
  );
}
