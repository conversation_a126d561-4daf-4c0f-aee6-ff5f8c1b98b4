import { motion, useScroll, useTransform } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { useParallax } from "../../hooks/use-parallax";

const features = [
  {
    title: "Nahtlose Integration",
    description:
      "Direkt in Microsoft Word eingebunden für ein vertrautes und intuitives Nutzungserlebnis.",
    icon: "📝",
    gradient: "from-blue-500 to-cyan-500",
  },
  {
    title: "Strukturierte Prüftabellen",
    description:
      "Wandeln Sie Dokumente in klar strukturierte Tabellen mit Abschnitten für Originaltext, Kommentare, Änderungsvorschläge und finale Vereinbarungen um.",
    icon: "📊",
    gradient: "from-purple-500 to-pink-500",
  },
  {
    title: "Ein-Klick-Textübertragung",
    description:
      "Übertragen Sie Inhalte mühelos zwischen Bearbeitungsstufen – Formatierungen und Änderungsverfolgung bleiben erhalten.",
    icon: "🔄",
    gradient: "from-green-500 to-emerald-500",
  },
  {
    title: "Visuelle Statusanzeigen",
    description:
      'Markieren Sie Zeilen je nach Bearbeitungsstatus – "Kritisch", "In Bearbeitung" oder "Abgeschlossen".',
    icon: "🔍",
    gradient: "from-orange-500 to-yellow-500",
  },
  {
    title: "Zurück zur Dokumentenansicht",
    description:
      "Nach der Überprüfung können Tabelleninhalte wieder in ein normales Word-Dokument umgewandelt werden – inklusive aller Änderungen.",
    icon: "📄",
    gradient: "from-red-500 to-orange-500",
  },
  {
    title: "Plattformübergreifende Nutzung",
    description:
      "Funktioniert reibungslos sowohl in der Desktop- als auch in der Online-Version von Microsoft Word.",
    icon: "💻",
    gradient: "from-blue-500 to-indigo-500",
  },
];

const cardVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: i * 0.1,
      duration: 0.5,
      ease: [0.48, 0.15, 0.25, 0.96],
    },
  }),
};

export default function Features() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0, 1], [0, -500]);
  const parallaxRef = useParallax(25);

  return (
    <section id="features" className="relative py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" />

      <motion.div
        ref={ref}
        style={{ y }}
        className="container mx-auto px-6 relative z-10"
      >
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <motion.h2
            ref={parallaxRef}
            className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 "
          >
            Warum Conalys?
          </motion.h2>
          <p className="text-xl text-gray-400 max-w-2xl mx-auto">
            Entdecken Sie die Vorteile unserer innovativen Lösung für modernes
            Vertragsmanagement
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              custom={index}
              variants={cardVariants}
              initial="hidden"
              animate={inView ? "visible" : "hidden"}
              whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
              className="relative group"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300" />
              <div className="relative bg-gray-800/50 backdrop-blur-xl rounded-2xl p-8 border border-gray-700/50 h-full transform transition-all duration-300 group-hover:translate-y-[-4px]">
                <div
                  className={`text-4xl mb-6 bg-gradient-to-r ${feature.gradient} rounded-full w-16 h-16 flex items-center justify-center`}
                >
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
                  {feature.title}
                </h3>
                <p className="text-gray-400 leading-relaxed">
                  {feature.description}
                </p>
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </section>
  );
}
