import { useEffect, useState } from "react";

const words = [
  "Dokumentenbearbeitung",
  "Vertragsmanagement",
  // "Dokumentenprüfung",
  "Zusammenarbeit",
];

export default function TypewriterEffect() {
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [currentText, setCurrentText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const word = words[currentWordIndex];
    const timeout = setTimeout(
      () => {
        if (!isDeleting) {
          if (currentText.length < word.length) {
            setCurrentText(word.slice(0, currentText.length + 1));
          } else {
            setTimeout(() => setIsDeleting(true), 1500);
          }
        } else {
          if (currentText.length === 0) {
            setIsDeleting(false);
            setCurrentWordIndex((prev) => (prev + 1) % words.length);
          } else {
            setCurrentText(word.slice(0, currentText.length - 1));
          }
        }
      },
      isDeleting ? 50 : 100
    );

    return () => clearTimeout(timeout);
  }, [currentText, isDeleting, currentWordIndex]);

  return (
    <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400 absolute left-1/2 -translate-x-1/2 leading-[1.4]">
      {currentText || "\u00A0"}
    </span>
  );
}
