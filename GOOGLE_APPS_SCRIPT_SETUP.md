# 📧 Google Apps Script Email Automation Setup Guide

This guide will help you set up automated email sending for your contact form using Google Apps Script.

## 🚀 **Step 1: Create Google Apps Script Project**

1. **Go to Google Apps Script**: Visit [script.google.com](https://script.google.com)
2. **Create New Project**: Click "New project"
3. **Name Your Project**: Rename it to "Conalys Contact Form Handler"

## 📝 **Step 2: Add the Script Code**

1. **Delete Default Code**: Remove the default `myFunction()` code
2. **Copy Script**: Copy the entire content from `google-apps-script/contact-form-handler.js`
3. **Paste Code**: Paste it into the script editor
4. **Update Configuration**: Modify the CONFIG object at the top:

```javascript
const CONFIG = {
  ADMIN_EMAIL: "<EMAIL>", // ← Change this to your admin email
  ADMIN_NAME: "Conalys Support Team",
  COMPANY_NAME: "Conalys",
  COMPANY_WEBSITE: "https://conalys.com", // ← Change to your website

  // Add your domain to allowed origins
  ALLOWED_ORIGINS: [
    "http://localhost:5173",
    "http://localhost:3000",
    "https://conalys.com", // ← Your production domain
    "https://www.conalys.com", // ← Your www domain
  ],
};
```

## 🔧 **Step 3: Deploy as Web App**

1. **Save Project**: Press `Ctrl+S` or click the save icon
2. **Deploy**: Click "Deploy" → "New deployment"
3. **Choose Type**: Select "Web app" from the gear icon
4. **Configure Deployment**:
   - **Description**: "Contact Form Email Handler"
   - **Execute as**: "Me"
   - **Who has access**: "Anyone"
5. **Deploy**: Click "Deploy"
6. **Copy URL**: Copy the Web App URL (it looks like: `https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec`)

## 🔗 **Step 4: Update React App**

1. **Open**: `src/hooks/use-contact-form.ts`
2. **Replace URL**: Update the `GOOGLE_SCRIPT_URL` constant:

```typescript
// Replace YOUR_SCRIPT_ID with the actual ID from your Web App URL
const GOOGLE_SCRIPT_URL =
  "https://script.google.com/macros/s/YOUR_SCRIPT_ID/exec";
```

## ✅ **Step 5: Test the Setup**

1. **Start Development Server**: `npm run dev`
2. **Navigate to Contact Section**: Go to `/#contact`
3. **Fill Out Form**: Enter test data
4. **Submit**: Click "Absenden"
5. **Check Results**:
   - Form should show success message
   - Admin should receive email with form details
   - User should receive confirmation email

## 📧 **Email Templates - Website Theme Design**

### **🎨 Admin Email Design:**

**Modern Dark Theme with Blue/Purple Gradients**

- ✅ **Dark Background**: Matches your website's `#0f172a` to `#1e293b` gradient
- ✅ **Blue/Purple Gradients**: Same `#3b82f6` to `#8b5cf6` gradients as your site
- ✅ **Glass-morphism Cards**: Translucent cards with backdrop blur effects
- ✅ **Modern Icons**: Emoji icons with gradient backgrounds
- ✅ **Professional Layout**: Clean, organized contact details with visual hierarchy
- ✅ **Responsive Design**: Works perfectly on mobile and desktop
- ✅ **Action Button**: Gradient "Reply" button for quick response

**Visual Elements:**

- 🚀 Gradient header with grid pattern overlay
- 👤📧💬⏰ Icon-based contact details layout
- 💌 Message card with dark theme styling
- 📧 Call-to-action reply button

### **🎉 User Confirmation Email Design:**

**Attractive Thank You Experience**

- ✅ **Company Branding**: Large logo area with rocket emoji
- ✅ **Success Animation**: Green checkmark with celebration theme
- ✅ **Personal Welcome**: "Hallo [Name]!" with warm greeting
- ✅ **Process Timeline**: 3-step visual guide of what happens next
- ✅ **Reference ID**: Unique tracking number for the submission
- ✅ **Contact Options**: Direct email link for urgent questions
- ✅ **Website Link**: Branded button to visit your website

**Visual Elements:**

- 🚀 Company logo placeholder with gradient background
- ✅ Success checkmark with green accent
- 📋 Submission summary card
- 🔄 Step-by-step process visualization
- 💬 Contact information card
- 🌐 Website visit button

**Content Highlights:**

- Personalized greeting with user's name
- Professional "Thank you for your interest" message
- Clear timeline: "We'll respond within 24 hours"
- Reference ID for tracking
- Step-by-step explanation of next steps
- Direct contact options for urgent matters

## 🛠️ **Customization Options**

### **Email Templates**

You can customize the email templates by modifying:

- `sendAdminEmail()` function for admin notifications
- `sendUserEmail()` function for user confirmations

### **Validation Rules**

Modify the `validateForm()` function in the React hook to add:

- Phone number validation
- Custom field requirements
- Message length limits

### **CORS Settings**

Add your domains to `ALLOWED_ORIGINS` in the CONFIG object.

## 🔒 **Security Features**

- ✅ **Email Validation**: Validates email format
- ✅ **Required Fields**: Ensures all fields are filled
- ✅ **CORS Protection**: Only allows requests from specified domains
- ✅ **Input Sanitization**: Prevents XSS attacks
- ✅ **Rate Limiting**: Google Apps Script has built-in rate limiting

## 🐛 **Troubleshooting**

### **Common Issues:**

1. **"Script function not found"**

   - Make sure you deployed as "Web app"
   - Check that the script is saved

2. **CORS Errors**

   - Add your domain to `ALLOWED_ORIGINS`
   - Make sure the domain matches exactly

3. **Emails Not Sending**

   - Check Gmail quota (100 emails/day for free accounts)
   - Verify admin email address is correct
   - Check spam folders

4. **Form Not Submitting**
   - Check browser console for errors
   - Verify the Web App URL is correct
   - Ensure the script is deployed with "Anyone" access

### **Testing in Development:**

- The script allows `localhost:5173` by default
- Check browser console for detailed error messages
- Use browser network tab to see request/response

## 📊 **Monitoring**

### **View Execution Logs:**

1. Go to Google Apps Script project
2. Click "Executions" in the left sidebar
3. View logs for each form submission

### **Email Delivery:**

- Admin emails go to the configured admin address
- User confirmations go to the email provided in the form
- Check spam folders if emails don't arrive

## 🎯 **Production Checklist**

Before going live, ensure:

- [ ] Admin email is correct
- [ ] Production domain is in ALLOWED_ORIGINS
- [ ] Web App URL is updated in React app
- [ ] Test form submission works
- [ ] Both admin and user emails are received
- [ ] Email templates look good on mobile
- [ ] Spam filters don't block emails

## 💡 **Advanced Features**

You can extend the script to:

- Save submissions to Google Sheets
- Send different emails based on subject
- Add file attachment support
- Integrate with CRM systems
- Add auto-responder sequences

---

**🎉 That's it! Your contact form now has professional email automation!**
