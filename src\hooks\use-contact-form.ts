import { useState } from "react";

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

interface UseContactFormReturn {
  formData: ContactFormData;
  isLoading: boolean;
  isSuccess: boolean;
  error: string | null;
  updateField: (field: keyof ContactFormData, value: string) => void;
  submitForm: () => Promise<void>;
  resetForm: () => void;
}

// Replace this with your actual Google Apps Script Web App URL
const GOOGLE_SCRIPT_URL =
  "https://script.google.com/macros/s/AKfycbzLDeWRECgWG7kn-uyKPvqVj3UcFH4fbzCeyH9I8BCDT_1Hae8h1yB600NEwsLiVjpQNw/exec";
// "https://script.google.com/macros/s/AKfycbwY9zuKHC5ZvHXBf92qWzvQ5j01JbeqPfzTefLOqNNayClVNep1uKHC5QgvxcWUI-qMdA/exec";

export function useContactForm(): UseContactFormReturn {
  const [formData, setFormData] = useState<ContactFormData>({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateField = (field: keyof ContactFormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (error) {
      setError(null);
    }

    // Clear success when user starts editing
    if (isSuccess) {
      setIsSuccess(false);
    }
  };

  const validateForm = (): string | null => {
    if (!formData.name.trim()) {
      return "Name ist erforderlich";
    }

    if (!formData.email.trim()) {
      return "E-Mail ist erforderlich";
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      return "Ungültige E-Mail-Adresse";
    }

    if (!formData.subject.trim()) {
      return "Betreff ist erforderlich";
    }

    if (!formData.message.trim()) {
      return "Nachricht ist erforderlich";
    }

    if (formData.message.trim().length < 10) {
      return "Nachricht muss mindestens 10 Zeichen lang sein";
    }

    return null;
  };

  // const submitForm = async (): Promise<void> => {
  //   setIsLoading(true);
  //   setError(null);
  //   setIsSuccess(false);

  //   try {
  //     // Validate form
  //     const validationError = validateForm();
  //     if (validationError) {
  //       setError(validationError);
  //       setIsLoading(false);
  //       return;
  //     }

  //     // Prepare data for submission
  //     const submissionData = {
  //       ...formData,
  //       origin: window.location.origin,
  //       timestamp: new Date().toISOString(),
  //     };

  //     // Submit to Google Apps Script
  //     console.log("Submitting to:", GOOGLE_SCRIPT_URL);
  //     console.log("Submission data:", submissionData);

  //     // Use form data instead of JSON to avoid CORS preflight
  //     const postData = new FormData();
  //     Object.keys(submissionData).forEach((key) => {
  //       postData.append(
  //         key,
  //         String(submissionData[key as keyof typeof submissionData])
  //       );
  //     });

  //     const response = await fetch(GOOGLE_SCRIPT_URL, {
  //       method: "POST",
  //       body: submissionData,
  //       mode: "no-cors",
  //     });

  //     console.log("Response status:", response.status);
  //     console.log("Response type:", response.type);

  //     // With no-cors mode, we can't read the response
  //     // But if the request completes without error, assume success
  //     if (response.type === "opaque") {
  //       console.log("Request completed successfully");
  //       setIsSuccess(true);
  //       // Reset form after success
  //       setFormData({
  //         name: "",
  //         email: "",
  //         subject: "",
  //         message: "",
  //       });
  //     } else {
  //       // Assume success if we get here without error
  //       setIsSuccess(true);
  //       setFormData({
  //         name: "",
  //         email: "",
  //         subject: "",
  //         message: "",
  //       });
  //     }
  //   } catch (err) {
  //     console.error("Error submitting form:", err);
  //     setError(
  //       "Fehler beim Senden der Nachricht. Bitte versuchen Sie es später erneut."
  //     );
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  const submitForm = async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    setIsSuccess(false);

    try {
      // Validate form
      const validationError = validateForm();
      if (validationError) {
        setError(validationError);
        setIsLoading(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        origin: window.location.origin,
        // timestamp: new Date().toISOString(),
      };

      console.log("Submitting to:", GOOGLE_SCRIPT_URL);
      console.log("Submission data:", submissionData);

      // Send as JSON with proper headers
      // const response = await fetch(GOOGLE_SCRIPT_URL, {
      //   method: "POST",
      //   headers: {
      //     "Content-Type": "application/json",
      //     Origin: window.location.origin,
      //   },
      //   body: JSON.stringify(submissionData),
      //   // Important: Don't use 'no-cors' mode
      //   // mode: "cors",
      //   // credentials: "omit",
      // });

      fetch(GOOGLE_SCRIPT_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams(submissionData),
      })
        .then((res) => res.json())
        .then(async (data) => {
          console.log("Response data:", data);
          // Check if the response is OK (status 200-299)
          if (data) {
            console.log("Response data:", data);

            if (data.success) {
              setIsSuccess(true);
              // Reset form after success
              setFormData({
                name: "",
                email: "",
                subject: "",
                message: "",
              });
            } else {
              setError(data.error || "Unknown error occurred");
            }
          } else {
            // Handle HTTP errors
            setError(
              data?.error || `Server error: ${data?.status} ${data?.statusText}`
            );
          }
        })
        .catch((err) => console.error(err));

      // console.log("Response status:", response.status);
    } catch (err) {
      console.error("Error submitting form:", err);
      setError(
        "Fehler beim Senden der Nachricht. Bitte versuchen Sie es später erneut."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      email: "",
      subject: "",
      message: "",
    });
    setIsLoading(false);
    setIsSuccess(false);
    setError(null);
  };

  return {
    formData,
    isLoading,
    isSuccess,
    error,
    updateField,
    submitForm,
    resetForm,
  };
}
