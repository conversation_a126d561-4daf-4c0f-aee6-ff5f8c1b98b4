import { motion } from "framer-motion";

export default function FooterContact() {
  return (
    <section className="relative py-32 overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px] pointer-events-none" />

      <div className="container mx-auto px-6 text-center relative z-10">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-4xl md:text-5xl font-bold mb-10 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400"
        >
          Kontakt & Demo anfragen
        </motion.h2>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 justify-center items-center"
        >
          <motion.div
            whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            className="bg-gray-800 rounded-full shadow-lg p-6 flex items-center justify-center gap-4"
          >
            <span className="text-3xl">📧</span>
            <a
              href="mailto:<EMAIL>"
              className="text-gray-300 hover:text-white transition-colors"
            >
              <EMAIL>
            </a>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            className="bg-gray-800 rounded-full shadow-lg p-6 flex items-center justify-center gap-4"
          >
            <span className="text-3xl">📞</span>
            <a
              href="tel:+4916095470114"
              className="text-gray-300 hover:text-white transition-colors"
            >
              +49 160 954 70114
            </a>
          </motion.div>
          <motion.div
            whileHover={{ scale: 1.05, transition: { duration: 0.2 } }}
            className="bg-gray-800 rounded-full shadow-lg p-6 flex items-center justify-center gap-4"
          >
            <span className="text-3xl">🌐</span>
            <a
              href="https://www.conalys.de"
              target="_blank"
              rel="noopener noreferrer"
              className="text-gray-300 hover:text-white transition-colors"
            >
              www.conalys.de
            </a>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
