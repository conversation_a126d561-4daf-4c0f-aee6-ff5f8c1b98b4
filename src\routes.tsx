import { createBrowserRouter } from "react-router-dom";
import Index from "./pages/index";
import NotFound from "./pages/NotFound";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsConditions from "./pages/TermsConditions";
import ImprintPage from "./pages/Imprint";
import StartNowPage from "./pages/StartNowPage";
import AccessibilityStatement from "./pages/AccessibilityStatement";
import CancellationPolicy from "./pages/CancellationPolicy";

const routes = [
  {
    path: "/",
    element: <Index />,
  },
  {
    path: "/privacy",
    element: <PrivacyPolicy />,
  },
  {
    path: "/terms",
    element: <TermsConditions />,
  },
  {
    path: "/imprint",
    element: <ImprintPage />,
  },
  {
    path: "/start-now",
    element: <StartNowPage />,
  },
  {
    path: "/accessibility",
    element: <AccessibilityStatement />,
  },
  {
    path: "/cancellation",
    element: <CancellationPolicy />,
  },
  {
    path: "*",
    element: <NotFound />,
  },
];

export const router = createBrowserRouter(routes);
