import { motion } from "framer-motion";

export default function AccessibilityStatement() {
  return (
    <section className="py-32 bg-gray-950 text-gray-200">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-900/10 via-gray-900/50 to-gray-950" />

      {/* Animated Gradient Orbs */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-1/2 -left-1/2 w-full h-full bg-gradient-to-r from-blue-500/30 to-purple-500/30 blur-3xl animate-drift" />
        <div className="absolute -bottom-1/2 -right-1/2 w-full h-full bg-gradient-to-l from-cyan-500/30 to-blue-500/30 blur-3xl animate-drift-slow" />
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:64px_64px] [mask-image:radial-gradient(ellipse_50%_50%_at_50%_50%,black_40%,transparent_100%)]" />
      {/* <div className="absolute inset-0 bg-gradient-to-b from-gray-900/50 to-gray-950/80" />
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:32px_32px]" /> */}
      <div className="container max-w-3xl mx-auto  ">
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <div>
            <h1 className="text-3xl font-bold mb-8">
              Erklärung zur Barrierefreiheit für Dienstleistungen
            </h1>
            <div className="space-y-8">
              <section>
                <p>
                  Im Rahmen unserer Barrierefreiheitserklärung möchten wir Ihnen
                  einen Überblick über den Stand der Vereinbarkeit der unten
                  beschriebenen Dienstleistung(en) mit den Anforderungen der
                  Barrierefreiheit nach gesetzlichen Vorschriften (insbesondere
                  mit dem Barrierefreiheitsstärkungsgesetz - BFSG) geben.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Angaben zum Dienstleistungserbringer
                </h2>
                <p>
                  CONALYS
                  <br />
                  Dr. Bettina Oertel
                  <br />
                  Berbisdorfer Str. 16
                  <br />
                  09123 Chemnitz
                  <br />
                  Sachsen
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Allgemeine Beschreibung der Dienstleistung
                </h2>
                <p>
                  {/* Umsatzsteuer-Identifikationsnummer gem&auml;&szlig; &sect; 27
                  a Umsatzsteuergesetz:
                  <br /> */}
                  Lizenzierung eines Add-In für Microsoft Word
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Erläuterungen zur Durchführung der Dienstleistung
                </h2>
                <p>
                  Über unsere Website{" "}
                  <a
                    href="https://www.conalys.de"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-500 hover:underline cursor-pointer"
                  >
                    www.conalys.de{" "}
                  </a>
                  bieten wir ein Add-In für Microsoft Word zur kostenpflichtigen
                  Lizenzierung im Abbonnement an. Über unsere Website können Sie
                  das Abonnement kostenpflichtig abschließen und das Add-In
                  herunterladen.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  {/* Verbraucher&shy;streit&shy;beilegung/Universal&shy;schlichtungs&shy;stelle */}
                  Stand der Vereinbarkeit mit den Anforderungen
                </h2>
                <p>
                  Die oben genannte Dienstleistung ist vollständig mit dem
                  Barrierefreiheitsstärkungsgesetz (BFSG) vereinbar
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Erstellung der Barrierefreiheitserklärung
                </h2>
                <p>
                  Datum der Erstellung der Barrierefreiheitserklärung:
                  30.06.2025
                  <br />
                  Datum der letzten Überprüfung der o. g. Leistungen
                  hinsichtlich der Anforderungen zur Barrierefreiheit:
                  27.06.2025
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Einschätzung zum Stand der Barrierefreiheit
                </h2>
                <p>
                  Die Einschätzung zum Stand der Barrierefreiheit beruht auf
                  unserer Selbsteinschätzung.
                </p>
              </section>
              <section>
                <h2 className="text-xl font-semibold mb-3">
                  Feedbackmöglichkeit und Kontaktangaben
                </h2>
                <p>
                  Adresse:
                  <br />
                  Dr. Bettina Oertel
                  <br />
                  Berbisdorfer Str. 16
                  <br />
                  D-09123 Chemnitz
                  <br />
                  <br />
                  E-Mail:
                  <a
                    href="mailto:<EMAIL>"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-500 hover:underline cursor-pointer"
                  >
                    {" "}
                    <EMAIL>
                  </a>
                  <br />
                  <br />
                  Link zur Website:
                  <br />
                  <a
                    href="https://www.conalys.de/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-500 hover:underline cursor-pointer"
                  >
                    https://www.conalys.de/
                  </a>
                </p>
              </section>
            </div>

            {/* </div> */}
          </div>
        </motion.div>

        {/* <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="bg-gray-800/40 backdrop-blur-xl rounded-3xl p-12 border border-gray-700/50 hover:border-gray-600/50 transition-colors mt-5"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300 pointer-events-none" />
          <div>
            <h1 className="text-3xl font-bold mb-8">
              Feedbackmöglichkeit und Kontaktangaben
            </h1>

            <div className="space-y-8">
              <section>
                <p>
                  Adresse:
                  <br />
                  Dr. Bettina Oertel
                  <br />
                  Berbisdorfer Str. 16
                  <br />
                  D-09123 Chemnitz
                  <br />
                  <br />
                  E-Mail:
                  <a
                    href="mailto:<EMAIL>"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-500 hover:underline cursor-pointer"
                  >
                    {" "}
                    <EMAIL>
                  </a>
                  <br />
                  <br />
                  Link zur Website:
                  <br />
                  <a
                    href="https://www.conalys.de/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-orange-500 hover:underline cursor-pointer"
                  >
                    https://www.conalys.de/
                  </a>
                </p>
              </section>
            </div>
          </div>
        </motion.div> */}
      </div>
    </section>
  );
}
